package tests

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gochen/app/cqrs"
	"gochen/app/eventsourcing"
	"gochen/data/code"
	"gochen/domain/model"
	"gochen/domain/model/eventaggregate"
	"gochen/domain/repo"
	"gochen/domain/service"
	"gochen/event"
	testingtools "gochen/tools/testing"
)

// TestUser 测试用户实体
type TestUser struct {
	model.Entity
	Name  string `json:"name"`
	Email string `json:"email"`
}

func (u *TestUser) GetID() int64 {
	return u.ID
}

// TestOrder 测试订单聚合
type TestOrder struct {
	eventaggregate.EventSourcedAggregate
	CustomerID int64   `json:"customer_id"`
	Amount     float64 `json:"amount"`
	Status     string  `json:"status"`
}

func NewTestOrder(id int64) *TestOrder {
	return &TestOrder{
		EventSourcedAggregate: *eventaggregate.NewEventSourcedAggregate(id, "TestOrder"),
		Status:                "pending",
	}
}

func (o *TestOrder) CreateOrder(customerID int64, amount float64) error {
	o.CustomerID = customerID
	o.Amount = amount
	o.Status = "created"

	return o.RaiseEvent("OrderCreated", map[string]interface{}{
		"customer_id": customerID,
		"amount":      amount,
	})
}

func (o *TestOrder) ConfirmOrder() error {
	if o.Status != "created" {
		return assert.AnError
	}

	o.Status = "confirmed"
	return o.RaiseEvent("OrderConfirmed", map[string]interface{}{
		"order_id": o.ID,
	})
}

// TestCreateUserCommand 测试创建用户命令
type TestCreateUserCommand struct {
	cqrs.BaseCommand
	Name  string `json:"name"`
	Email string `json:"email"`
}

// TestCreateUserHandler 测试创建用户命令处理器
type TestCreateUserHandler struct {
	userRepo *testingtools.MockRepository[*TestUser]
}

func (h *TestCreateUserHandler) Handle(ctx context.Context, cmd TestCreateUserCommand) error {
	user := &TestUser{
		Entity: *model.NewEntity(code.MustNextID(), time.Now()),
		Name:   cmd.Name,
		Email:  cmd.Email,
	}

	return h.userRepo.Save(ctx, &user)
}

// TestGetUserQuery 测试获取用户查询
type TestGetUserQuery struct {
	cqrs.BaseQuery
	UserID int64 `json:"user_id"`
}

// TestGetUserHandler 测试获取用户查询处理器
type TestGetUserHandler struct {
	userRepo *testingtools.MockRepository[*TestUser]
}

func (h *TestGetUserHandler) Handle(ctx context.Context, query TestGetUserQuery) (*TestUser, error) {
	user, err := h.userRepo.Get(ctx, query.UserID)
	if err != nil {
		return nil, err
	}
	return *user, nil
}

// TestOrderProjection 测试订单投影
type TestOrderProjection struct {
	*event.BaseProjection
	orders map[int64]*TestOrderView
}

type TestOrderView struct {
	ID         int64     `json:"id"`
	CustomerID int64     `json:"customer_id"`
	Amount     float64   `json:"amount"`
	Status     string    `json:"status"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
}

func NewTestOrderProjection() *TestOrderProjection {
	return &TestOrderProjection{
		BaseProjection: event.NewBaseProjection("TestOrderProjection", []string{"OrderCreated", "OrderConfirmed"}),
		orders:         make(map[int64]*TestOrderView),
	}
}

func (p *TestOrderProjection) Handle(ctx context.Context, evt event.Event) error {
	switch evt.EventType {
	case "OrderCreated":
		return p.handleOrderCreated(evt)
	case "OrderConfirmed":
		return p.handleOrderConfirmed(evt)
	}
	return nil
}

func (p *TestOrderProjection) handleOrderCreated(evt event.Event) error {
	data := evt.Data.(map[string]interface{})

	p.orders[evt.AggregateID] = &TestOrderView{
		ID:         evt.AggregateID,
		CustomerID: int64(data["customer_id"].(float64)),
		Amount:     data["amount"].(float64),
		Status:     "created",
		CreatedAt:  evt.Timestamp,
		UpdatedAt:  evt.Timestamp,
	}

	return nil
}

func (p *TestOrderProjection) handleOrderConfirmed(evt event.Event) error {
	if order, exists := p.orders[evt.AggregateID]; exists {
		order.Status = "confirmed"
		order.UpdatedAt = evt.Timestamp
	}
	return nil
}

func (p *TestOrderProjection) Rebuild(ctx context.Context, events []event.Event) error {
	p.orders = make(map[int64]*TestOrderView)
	for _, evt := range events {
		if err := p.Handle(ctx, evt); err != nil {
			return err
		}
	}
	return nil
}

func (p *TestOrderProjection) GetOrders() []*TestOrderView {
	var orders []*TestOrderView
	for _, order := range p.orders {
		orders = append(orders, order)
	}
	return orders
}

// TestSimpleModel 测试极简模型
func TestSimpleModel(t *testing.T) {
	// 初始化ID生成器
	code.InitGenerator(1, 1)

	// 创建Mock仓库
	userRepo := testingtools.NewMockRepository[*TestUser]()

	// 创建服务
	userService := service.NewService("user", userRepo)

	ctx := context.Background()

	t.Run("创建用户", func(t *testing.T) {
		user := &TestUser{
			Entity: *model.NewEntity(code.MustNextID(), time.Now()),
			Name:   "张三",
			Email:  "<EMAIL>",
		}

		err := userService.Save(ctx, &user)
		assert.NoError(t, err)
		assert.Greater(t, user.ID, int64(0))
	})

	t.Run("获取用户", func(t *testing.T) {
		// 先创建用户
		user := &TestUser{
			Entity: *model.NewEntity(code.MustNextID(), time.Now()),
			Name:   "李四",
			Email:  "<EMAIL>",
		}
		err := userService.Save(ctx, &user)
		require.NoError(t, err)

		// 获取用户
		retrievedUser, err := userService.Get(ctx, user.ID)
		assert.NoError(t, err)
		assert.Equal(t, user.Name, (*retrievedUser).Name)
		assert.Equal(t, user.Email, (*retrievedUser).Email)
	})

	t.Run("批量操作", func(t *testing.T) {
		user1 := &TestUser{
			Entity: *model.NewEntity(code.MustNextID(), time.Now()),
			Name:   "用户1",
			Email:  "<EMAIL>",
		}
		user2 := &TestUser{
			Entity: *model.NewEntity(code.MustNextID(), time.Now()),
			Name:   "用户2",
			Email:  "<EMAIL>",
		}

		users := []*TestUser{user1, user2}

		// 批量保存
		err := userService.SaveBatch(ctx, users)
		assert.NoError(t, err)

		// 验证保存成功
		for _, user := range users {
			retrievedUser, err := userService.Get(ctx, user.ID)
			assert.NoError(t, err)
			assert.Equal(t, user.Name, (*retrievedUser).Name)
		}
	})

	t.Run("分页查询", func(t *testing.T) {
		options := &repo.QueryOptions{
			Page: 1,
			Size: 10,
		}

		result, err := userService.FindWithOptions(ctx, options)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.GreaterOrEqual(t, result.Total, int64(0))
	})
}

// TestCQRSModel 测试CQRS模型
func TestCQRSModel(t *testing.T) {
	// 初始化ID生成器
	code.InitGenerator(1, 1)

	// 创建CQRS应用服务
	cqrsService := cqrs.NewCQRSAppService()
	cqrsService.SetupDefaultMiddlewares()

	// 创建Mock仓库
	userRepo := testingtools.NewMockRepository[*TestUser]()

	// 注册命令处理器
	createUserHandler := &TestCreateUserHandler{userRepo: userRepo}
	err := cqrsService.RegisterCommandHandler("TestCreateUserCommand", createUserHandler)
	require.NoError(t, err)

	// 注册查询处理器
	getUserHandler := &TestGetUserHandler{userRepo: userRepo}
	err = cqrsService.RegisterQueryHandler("TestGetUserQuery", getUserHandler)
	require.NoError(t, err)

	ctx := context.Background()

	t.Run("执行命令", func(t *testing.T) {
		createCmd := TestCreateUserCommand{
			Name:  "王五",
			Email: "<EMAIL>",
		}
		createCmd.CommandType = "TestCreateUserCommand"

		err := cqrsService.ExecuteCommand(ctx, createCmd)
		assert.NoError(t, err)
	})

	t.Run("执行查询", func(t *testing.T) {
		// 先创建用户
		createCmd := TestCreateUserCommand{
			Name:  "赵六",
			Email: "<EMAIL>",
		}
		createCmd.CommandType = "TestCreateUserCommand"
		err := cqrsService.ExecuteCommand(ctx, createCmd)
		require.NoError(t, err)

		// 查询用户（这里简化处理，实际需要知道用户ID）
		// 在实际项目中，命令执行后应该返回创建的实体ID
	})
}

// TestEventSourcingModel 测试Event Sourcing模型
func TestEventSourcingModel(t *testing.T) {
	// 初始化ID生成器
	code.InitGenerator(1, 1)

	// 创建集成测试助手
	helper := testingtools.NewIntegrationTestHelper()
	defer helper.Clear()

	// 创建Event Sourcing应用服务
	esService := eventsourcing.NewEventSourcingAppService(
		helper.GetEventStore(),
		helper.GetEventBus(),
		helper.GetSnapshotStore(),
	)

	ctx := context.Background()

	// 启动服务
	err := esService.Start(ctx)
	require.NoError(t, err)
	defer esService.Stop()

	// 注册聚合
	esService.RegisterAggregate("TestOrder", func(id int64) eventaggregate.IEventSourcedAggregate {
		return NewTestOrder(id)
	})

	// 注册投影
	orderProjection := NewTestOrderProjection()
	err = esService.RegisterProjection(orderProjection)
	require.NoError(t, err)

	// 启动投影
	err = esService.StartProjection(ctx, "TestOrderProjection")
	require.NoError(t, err)

	t.Run("创建和操作聚合", func(t *testing.T) {
		// 创建订单
		order := NewTestOrder(code.MustNextID())

		// 创建订单
		err := order.CreateOrder(123, 99.99)
		assert.NoError(t, err)

		// 确认订单
		err = order.ConfirmOrder()
		assert.NoError(t, err)

		// 保存聚合
		err = esService.SaveAggregate(ctx, order)
		assert.NoError(t, err)

		// 验证事件
		events := order.GetUncommittedEvents()
		assert.Len(t, events, 0) // 保存后应该清空未提交事件

		// 获取事件历史
		eventHistory, err := esService.GetEventHistory(ctx, order.GetID())
		assert.NoError(t, err)
		assert.Len(t, eventHistory, 2) // 应该有两个事件
	})

	t.Run("投影处理", func(t *testing.T) {
		// 等待事件处理
		time.Sleep(100 * time.Millisecond)

		// 检查投影结果
		orders := orderProjection.GetOrders()
		assert.GreaterOrEqual(t, len(orders), 1)

		if len(orders) > 0 {
			order := orders[0]
			assert.Equal(t, "confirmed", order.Status)
			assert.Equal(t, 99.99, order.Amount)
		}
	})

	t.Run("聚合重建", func(t *testing.T) {
		// 创建新的聚合实例
		orderID := int64(1) // 假设第一个订单的ID是1
		newOrder := NewTestOrder(orderID)

		// 从事件历史重建
		events, err := esService.GetEventHistory(ctx, orderID)
		require.NoError(t, err)

		if len(events) > 0 {
			err = newOrder.LoadFromHistory(events)
			assert.NoError(t, err)
			assert.Equal(t, "confirmed", newOrder.Status)
			assert.Equal(t, 99.99, newOrder.Amount)
		}
	})
}

// TestPerformance 性能测试
func TestPerformance(t *testing.T) {
	if testing.Short() {
		t.Skip("跳过性能测试")
	}

	helper := testingtools.NewPerformanceTestHelper()

	t.Run("创建聚合性能", func(t *testing.T) {
		helper.Start()

		for i := 0; i < 1000; i++ {
			order := NewTestOrder(int64(i))
			order.CreateOrder(123, 99.99)
		}

		helper.Stop()

		// 验证性能要求（1000个聚合创建应该在100ms内完成）
		assert.Less(t, helper.Duration(), 100*time.Millisecond)
	})

	t.Run("事件存储性能", func(t *testing.T) {
		eventStore := event.NewMemoryEventStore()
		ctx := context.Background()

		helper.Start()

		for i := 0; i < 1000; i++ {
			events := []event.Event{
				*event.NewEvent(int64(i), "TestEvent", 1, map[string]interface{}{
					"data": "test",
				}),
			}
			eventStore.SaveEvents(ctx, int64(i), events, 0)
		}

		helper.Stop()

		// 验证性能要求（1000个事件保存应该在50ms内完成）
		assert.Less(t, helper.Duration(), 50*time.Millisecond)
	})
}

// BenchmarkEventStore 事件存储基准测试
func BenchmarkEventStore(b *testing.B) {
	eventStore := event.NewMemoryEventStore()
	ctx := context.Background()

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		events := []event.Event{
			*event.NewEvent(int64(i), "BenchmarkEvent", 1, map[string]interface{}{
				"data": "benchmark",
			}),
		}
		eventStore.SaveEvents(ctx, int64(i), events, 0)
	}
}

// BenchmarkAggregateCreation 聚合创建基准测试
func BenchmarkAggregateCreation(b *testing.B) {
	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		order := NewTestOrder(int64(i))
		order.CreateOrder(123, 99.99)
	}
}
