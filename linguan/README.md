# Linguan - GoChen框架三种业务模型演示项目

[![Go Version](https://img.shields.io/badge/Go-1.21+-blue.svg)](https://golang.org)
[![Framework](https://img.shields.io/badge/Framework-GoChen-green.svg)](https://github.com/synebula-myths/gaea)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

## 📖 项目简介

Linguan 是基于 GoChen 框架开发的演示项目，展示了三种不同复杂度的业务模型在同一个应用中的完美融合：

- 🚀 **用户管理** - 极简模型（Simple Model）
- ⚡ **权限管理** - CQRS模型（Command Query Responsibility Segregation）
- 🔄 **任务管理** - Event Sourcing模型（事件溯源）

## 🎯 设计理念

### 渐进式架构
根据业务复杂度选择合适的架构模式：
- **简单业务** → 极简模型（快速开发）
- **复杂业务** → CQRS模型（读写分离）
- **审计需求** → Event Sourcing（完整历史）

### 统一开发体验
- 一致的API设计风格
- 统一的错误处理机制
- 标准化的响应格式

## 🚀 快速开始

### 环境要求
- Go 1.21+
- Git

### 安装运行
```bash
# 克隆项目
git clone <repository-url>
cd linguan

# 安装依赖
go mod tidy

# 运行演示应用
go run cmd/demo_main.go
```

### 访问应用
- 🌐 **系统信息**: http://localhost:8081/info
- 💚 **健康检查**: http://localhost:8081/health
- 📊 **数据统计**: 实时显示各模型的数据量

### 运行测试
```bash
# 运行完整API测试
./test_api.sh
```

## 📚 业务模型详解

### 1. 用户管理（极简模型）
**适用场景**: 简单的CRUD操作，快速开发需求

**特点**:
- 直接的数据库操作
- 简单的业务逻辑
- 快速的开发周期

**API示例**:
```bash
# 创建用户
curl -X POST http://localhost:8081/api/users \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "password123",
    "nickname": "测试用户"
  }'

# 用户登录
curl -X POST http://localhost:8081/api/users/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123"
  }'
```

### 2. 权限管理（CQRS模型）
**适用场景**: 复杂的业务逻辑，需要读写分离

**特点**:
- 命令查询分离
- 复杂业务逻辑处理
- 高性能读写操作

**API示例**:
```bash
# 创建权限
curl -X POST http://localhost:8081/api/permissions \
  -H "Content-Type: application/json" \
  -d '{
    "code": "user.create",
    "name": "创建用户",
    "description": "创建新用户的权限",
    "resource": "user",
    "action": "create",
    "category": "user_management"
  }'

# 创建角色
curl -X POST http://localhost:8081/api/roles \
  -H "Content-Type: application/json" \
  -d '{
    "code": "developer",
    "name": "开发者",
    "description": "开发者角色",
    "category": "development"
  }'
```

### 3. 任务管理（Event Sourcing模型）
**适用场景**: 需要完整审计历史，事件驱动业务

**特点**:
- 完整的事件历史
- 状态重建能力
- 强一致性保证

**API示例**:
```bash
# 创建任务
curl -X POST http://localhost:8081/api/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "title": "优化数据库性能",
    "description": "分析并优化数据库查询性能",
    "priority": "high",
    "assignee_id": 1,
    "project_id": 1,
    "tags": ["数据库", "性能", "优化"]
  }'

# 任务生命周期管理
curl -X POST http://localhost:8081/api/tasks/{id}/start    # 开始任务
curl -X PUT http://localhost:8081/api/tasks/{id}/progress \
  -d '{"progress": 50}'                                     # 更新进度
curl -X POST http://localhost:8081/api/tasks/{id}/complete # 完成任务
```

## 🏗️ 项目结构

```
linguan/
├── cmd/
│   ├── demo_main.go          # 演示应用（内存存储）
│   └── main.go               # 完整应用（数据库存储）
├── internal/
│   ├── api/                  # API控制器层
│   │   ├── user_controller.go
│   │   ├── permission_controller.go
│   │   ├── task_controller.go
│   │   └── response.go
│   ├── handler/              # CQRS处理器
│   │   ├── permission_handler.go
│   │   └── permission_query_handler.go
│   ├── model/                # 数据模型
│   │   ├── user.go           # 极简模型
│   │   ├── permission_cqrs.go # CQRS模型
│   │   └── task_eventsourcing.go # Event Sourcing模型
│   ├── repository/           # 数据仓库
│   │   ├── user_repository.go
│   │   └── task_repository.go
│   └── service/              # 业务服务
│       ├── user_service.go
│       ├── permission_service.go
│       └── task_service.go
├── test_api.sh               # API测试脚本
├── PROJECT_SUMMARY.md        # 项目总结
└── README.md                 # 项目文档
```

## 🔧 技术栈

- **框架**: GoChen v1.0
- **Web**: Gin
- **存储**: 内存存储（演示版）/ SQLite（完整版）
- **架构**: DDD + CQRS + Event Sourcing
- **API**: RESTful

## 📊 API文档

### 用户管理 API
| 方法 | 路径 | 描述 |
|------|------|------|
| POST | `/api/users` | 创建用户 |
| GET | `/api/users` | 获取用户列表 |
| GET | `/api/users/:id` | 获取用户详情 |
| PUT | `/api/users/:id` | 更新用户信息 |
| DELETE | `/api/users/:id` | 删除用户 |
| POST | `/api/users/login` | 用户登录 |

### 权限管理 API
| 方法 | 路径 | 描述 |
|------|------|------|
| POST | `/api/permissions` | 创建权限 |
| GET | `/api/permissions` | 获取权限列表 |
| GET | `/api/permissions/:id` | 获取权限详情 |
| POST | `/api/roles` | 创建角色 |
| GET | `/api/roles` | 获取角色列表 |
| GET | `/api/roles/:id` | 获取角色详情 |

### 任务管理 API
| 方法 | 路径 | 描述 |
|------|------|------|
| POST | `/api/tasks` | 创建任务 |
| GET | `/api/tasks` | 获取任务列表 |
| GET | `/api/tasks/:id` | 获取任务详情 |
| POST | `/api/tasks/:id/start` | 开始任务 |
| PUT | `/api/tasks/:id/progress` | 更新进度 |
| POST | `/api/tasks/:id/complete` | 完成任务 |

## 🧪 测试

### 自动化测试
```bash
# 运行完整API测试套件
./test_api.sh
```

### 手动测试
```bash
# 健康检查
curl http://localhost:8081/health

# 系统信息
curl http://localhost:8081/info
```

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

- [GoChen框架](https://github.com/synebula-myths/gaea) - 提供强大的业务模型支持
- [Gin](https://github.com/gin-gonic/gin) - 高性能的Go Web框架
- Go社区 - 优秀的生态系统

## 📞 联系我们

- 项目地址: [GitHub Repository]
- 问题反馈: [Issues]
- 文档: [Wiki]

---

**让Go开发更简单，让架构更清晰！** 🚀
