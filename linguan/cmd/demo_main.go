package main

import (
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"

	"gochen/data/code"
	"linguan/internal/model"
)

// 简化的内存存储演示
var (
	users       = make(map[int64]*model.User)
	permissions = make(map[int64]*model.Permission)
	roles       = make(map[int64]*model.Role)
	tasks       = make(map[int64]*model.TaskResponse)
	userRoles   = make(map[int64][]*model.UserRole)
)

func main() {
	// 初始化ID生成器
	code.InitGenerator(1, 1)

	// 初始化演示数据
	initDemoData()

	// 创建Gin引擎
	r := gin.Default()

	// 添加CORS中间件
	r.Use(corsMiddleware())

	// 注册API路由
	apiGroup := r.Group("/api")
	{
		// 用户管理（极简模型）
		apiGroup.POST("/users", createUser)
		apiGroup.GET("/users", listUsers)
		apiGroup.GET("/users/:id", getUser)
		apiGroup.POST("/users/login", loginUser)

		// 权限管理（CQRS模型）
		apiGroup.POST("/permissions", createPermission)
		apiGroup.GET("/permissions", listPermissions)
		apiGroup.GET("/permissions/:id", getPermission)

		// 角色管理
		apiGroup.POST("/roles", createRole)
		apiGroup.GET("/roles", listRoles)
		apiGroup.GET("/roles/:id", getRole)

		// 任务管理（Event Sourcing模型）
		apiGroup.POST("/tasks", createTask)
		apiGroup.GET("/tasks", listTasks)
		apiGroup.GET("/tasks/:id", getTask)
		apiGroup.POST("/tasks/:id/start", startTask)
		apiGroup.PUT("/tasks/:id/progress", updateTaskProgress)
		apiGroup.POST("/tasks/:id/complete", completeTask)
	}

	// 添加健康检查端点
	r.GET("/health", healthCheck)

	// 添加系统信息端点
	r.GET("/info", systemInfo)

	// 启动HTTP服务器
	log.Println("🚀 服务器启动在 :8081")
	log.Println("📚 系统信息: http://localhost:8081/info")
	log.Println("💚 健康检查: http://localhost:8081/health")
	log.Println("👤 用户管理: http://localhost:8081/api/users")
	log.Println("🔐 权限管理: http://localhost:8081/api/permissions")
	log.Println("📋 任务管理: http://localhost:8081/api/tasks")

	if err := r.Run(":8081"); err != nil {
		log.Fatalf("启动服务器失败: %v", err)
	}
}

// initDemoData 初始化演示数据
func initDemoData() {
	// 创建演示用户
	user1 := &model.User{
		Username: "admin",
		Email:    "<EMAIL>",
		Password: "admin123",
		Nickname: "管理员",
		Status:   "active",
	}
	user1.ID = code.MustNextID()
	user1.CreatedAt = time.Now()
	user1.UpdatedAt = time.Now()
	users[user1.ID] = user1

	user2 := &model.User{
		Username: "user1",
		Email:    "<EMAIL>",
		Password: "user123",
		Nickname: "普通用户",
		Status:   "active",
	}
	user2.ID = code.MustNextID()
	user2.CreatedAt = time.Now()
	user2.UpdatedAt = time.Now()
	users[user2.ID] = user2

	// 创建演示权限
	perm1 := &model.Permission{
		Code:        "user.read",
		Name:        "查看用户",
		Description: "查看用户信息的权限",
		Resource:    "user",
		Action:      "read",
		Category:    "user_management",
		Status:      "active",
	}
	perm1.ID = code.MustNextID()
	perm1.CreatedAt = time.Now()
	perm1.UpdatedAt = time.Now()
	permissions[perm1.ID] = perm1

	perm2 := &model.Permission{
		Code:        "task.manage",
		Name:        "任务管理",
		Description: "管理任务的权限",
		Resource:    "task",
		Action:      "manage",
		Category:    "task_management",
		Status:      "active",
	}
	perm2.ID = code.MustNextID()
	perm2.CreatedAt = time.Now()
	perm2.UpdatedAt = time.Now()
	permissions[perm2.ID] = perm2

	// 创建演示角色
	role1 := &model.Role{
		Code:        "admin",
		Name:        "管理员",
		Description: "系统管理员角色",
		Category:    "system",
		Status:      "active",
	}
	role1.ID = code.MustNextID()
	role1.CreatedAt = time.Now()
	role1.UpdatedAt = time.Now()
	roles[role1.ID] = role1

	// 创建演示任务
	task1 := &model.TaskResponse{
		ID:          code.MustNextID(),
		Title:       "完成项目文档",
		Description: "编写项目的技术文档和用户手册",
		Status:      model.TaskStatusPending,
		Priority:    model.TaskPriorityHigh,
		AssigneeID:  user2.ID,
		CreatorID:   user1.ID,
		ProjectID:   1,
		Progress:    0,
		Tags:        []string{"文档", "重要"},
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		Version:     1,
	}
	tasks[task1.ID] = task1

	task2 := &model.TaskResponse{
		ID:          code.MustNextID(),
		Title:       "代码审查",
		Description: "审查新功能的代码实现",
		Status:      model.TaskStatusInProgress,
		Priority:    model.TaskPriorityMedium,
		AssigneeID:  user1.ID,
		CreatorID:   user1.ID,
		ProjectID:   1,
		Progress:    50,
		Tags:        []string{"代码", "审查"},
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		Version:     2,
	}
	now := time.Now()
	task2.StartedAt = &now
	tasks[task2.ID] = task2

	log.Println("✅ 演示数据初始化完成")
	log.Printf("👥 用户数量: %d", len(users))
	log.Printf("🔐 权限数量: %d", len(permissions))
	log.Printf("👑 角色数量: %d", len(roles))
	log.Printf("📋 任务数量: %d", len(tasks))
}

// corsMiddleware CORS中间件
func corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	}
}

// APIResponse 统一API响应结构
type APIResponse struct {
	Code      int         `json:"code"`
	Message   string      `json:"message"`
	Data      interface{} `json:"data,omitempty"`
	Error     string      `json:"error,omitempty"`
	Timestamp time.Time   `json:"timestamp"`
}

// NewSuccessResponse 创建成功响应
func NewSuccessResponse(message string, data interface{}) *APIResponse {
	return &APIResponse{
		Code:      200,
		Message:   message,
		Data:      data,
		Timestamp: time.Now(),
	}
}

// NewErrorResponse 创建错误响应
func NewErrorResponse(message, error string) *APIResponse {
	return &APIResponse{
		Code:      400,
		Message:   message,
		Error:     error,
		Timestamp: time.Now(),
	}
}

// healthCheck 健康检查
func healthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":    "healthy",
		"timestamp": time.Now(),
		"version":   "1.0.0",
		"models": gin.H{
			"user_model":       "simple",
			"permission_model": "cqrs",
			"task_model":       "event_sourcing",
		},
		"data_counts": gin.H{
			"users":       len(users),
			"permissions": len(permissions),
			"roles":       len(roles),
			"tasks":       len(tasks),
		},
	})
}

// systemInfo 系统信息
func systemInfo(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"name":        "Linguan",
		"description": "基于GoChen框架的三种业务模型演示项目",
		"version":     "1.0.0",
		"framework":   "GoChen",
		"storage":     "内存存储（演示版）",
		"models": gin.H{
			"user": gin.H{
				"type":        "极简模型",
				"description": "用户管理采用极简模型，支持快速CRUD操作",
				"features": []string{
					"用户注册登录",
					"用户信息管理",
					"用户状态控制",
					"内存存储",
				},
				"count": len(users),
			},
			"permission": gin.H{
				"type":        "CQRS模型",
				"description": "权限管理采用CQRS模型，命令查询分离",
				"features": []string{
					"权限管理",
					"角色管理",
					"用户角色分配",
					"权限检查",
					"命令查询分离",
				},
				"counts": gin.H{
					"permissions": len(permissions),
					"roles":       len(roles),
				},
			},
			"task": gin.H{
				"type":        "Event Sourcing模型",
				"description": "任务管理采用Event Sourcing模型，完整事件溯源",
				"features": []string{
					"任务生命周期管理",
					"事件溯源",
					"任务历史追踪",
					"状态管理",
				},
				"count": len(tasks),
			},
		},
		"endpoints": gin.H{
			"health": "/health",
			"info":   "/info",
			"api": gin.H{
				"users":       "/api/users",
				"permissions": "/api/permissions",
				"roles":       "/api/roles",
				"tasks":       "/api/tasks",
			},
		},
		"examples": gin.H{
			"create_user": gin.H{
				"method": "POST",
				"url":    "/api/users",
				"body": gin.H{
					"username": "testuser",
					"email":    "<EMAIL>",
					"password": "password123",
					"nickname": "测试用户",
				},
			},
			"create_task": gin.H{
				"method": "POST",
				"url":    "/api/tasks",
				"body": gin.H{
					"title":       "示例任务",
					"description": "这是一个示例任务",
					"priority":    "medium",
					"assignee_id": 1,
					"project_id":  1,
				},
			},
			"create_permission": gin.H{
				"method": "POST",
				"url":    "/api/permissions",
				"body": gin.H{
					"code":        "example.read",
					"name":        "示例读取权限",
					"description": "读取示例资源的权限",
					"resource":    "example",
					"action":      "read",
					"category":    "example",
				},
			},
		},
	})
}

// ===== 用户管理API（极简模型）=====

// createUser 创建用户
func createUser(c *gin.Context) {
	var req model.UserCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, NewErrorResponse("请求参数错误", err.Error()))
		return
	}

	// 检查用户名是否已存在
	for _, user := range users {
		if user.Username == req.Username {
			c.JSON(http.StatusBadRequest, NewErrorResponse("用户名已存在", req.Username))
			return
		}
		if user.Email == req.Email {
			c.JSON(http.StatusBadRequest, NewErrorResponse("邮箱已存在", req.Email))
			return
		}
	}

	// 创建用户
	user := req.ToUser()
	user.ID = code.MustNextID()
	users[user.ID] = user

	// 创建副本并隐藏密码用于响应
	userCopy := *user
	userCopy.HidePassword()
	var response model.UserResponse
	response.FromUser(&userCopy)

	c.JSON(http.StatusCreated, NewSuccessResponse("创建用户成功", response))
}

// listUsers 获取用户列表
func listUsers(c *gin.Context) {
	var userList []model.UserResponse
	for _, user := range users {
		// 创建副本避免修改原始数据
		userCopy := *user
		userCopy.HidePassword()
		var response model.UserResponse
		response.FromUser(&userCopy)
		userList = append(userList, response)
	}

	result := model.UserListResponse{
		Users:      userList,
		Total:      int64(len(userList)),
		Page:       1,
		Size:       len(userList),
		TotalPages: 1,
	}

	c.JSON(http.StatusOK, NewSuccessResponse("获取用户列表成功", result))
}

// getUser 获取用户
func getUser(c *gin.Context) {
	idStr := c.Param("id")
	var userID int64
	if _, err := fmt.Sscanf(idStr, "%d", &userID); err != nil {
		c.JSON(http.StatusBadRequest, NewErrorResponse("用户ID格式错误", err.Error()))
		return
	}

	user, exists := users[userID]
	if !exists {
		c.JSON(http.StatusNotFound, NewErrorResponse("用户不存在", fmt.Sprintf("ID: %d", userID)))
		return
	}

	// 创建副本避免修改原始数据
	userCopy := *user
	userCopy.HidePassword()
	var response model.UserResponse
	response.FromUser(&userCopy)

	c.JSON(http.StatusOK, NewSuccessResponse("获取用户成功", response))
}

// loginUser 用户登录
func loginUser(c *gin.Context) {
	var req model.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, NewErrorResponse("请求参数错误", err.Error()))
		return
	}

	// 查找用户
	var foundUser *model.User
	for _, user := range users {
		if user.Username == req.Username {
			foundUser = user
			break
		}
	}

	if foundUser == nil || foundUser.Password != req.Password {
		c.JSON(http.StatusUnauthorized, NewErrorResponse("用户名或密码错误", ""))
		return
	}

	if !foundUser.IsActive() {
		c.JSON(http.StatusUnauthorized, NewErrorResponse("用户账号已被禁用", ""))
		return
	}

	// 更新最后登录时间
	foundUser.UpdateLastLogin()

	// 生成token（简化处理）
	token := fmt.Sprintf("token_%d_%d", foundUser.ID, time.Now().Unix())

	// 创建副本并隐藏密码
	userCopy := *foundUser
	userCopy.HidePassword()
	var userResponse model.UserResponse
	userResponse.FromUser(&userCopy)

	loginResp := model.LoginResponse{
		User:  userResponse,
		Token: token,
	}

	c.JSON(http.StatusOK, NewSuccessResponse("登录成功", loginResp))
}

// ===== 权限管理API（CQRS模型）=====

// createPermission 创建权限
func createPermission(c *gin.Context) {
	var req model.CreatePermissionCommand
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, NewErrorResponse("请求参数错误", err.Error()))
		return
	}

	// 检查权限代码是否已存在
	for _, perm := range permissions {
		if perm.Code == req.Code {
			c.JSON(http.StatusBadRequest, NewErrorResponse("权限代码已存在", req.Code))
			return
		}
	}

	// 创建权限
	permission := &model.Permission{
		Code:        req.Code,
		Name:        req.Name,
		Description: req.Description,
		Resource:    req.Resource,
		Action:      req.Action,
		Category:    req.Category,
		Status:      "active",
	}
	permission.ID = code.MustNextID()
	permission.CreatedAt = time.Now()
	permission.UpdatedAt = time.Now()

	permissions[permission.ID] = permission

	c.JSON(http.StatusCreated, NewSuccessResponse("创建权限成功", nil))
}

// listPermissions 获取权限列表
func listPermissions(c *gin.Context) {
	var permissionList []model.PermissionResponse
	for _, perm := range permissions {
		permissionList = append(permissionList, model.PermissionResponse{
			ID:          perm.ID,
			Code:        perm.Code,
			Name:        perm.Name,
			Description: perm.Description,
			Resource:    perm.Resource,
			Action:      perm.Action,
			Category:    perm.Category,
			Status:      perm.Status,
			CreatedAt:   perm.CreatedAt,
			UpdatedAt:   perm.UpdatedAt,
		})
	}

	result := map[string]interface{}{
		"permissions": permissionList,
		"total":       len(permissionList),
		"page":        1,
		"size":        len(permissionList),
		"total_pages": 1,
	}

	c.JSON(http.StatusOK, NewSuccessResponse("获取权限列表成功", result))
}

// getPermission 获取权限
func getPermission(c *gin.Context) {
	idStr := c.Param("id")
	var permissionID int64
	if _, err := fmt.Sscanf(idStr, "%d", &permissionID); err != nil {
		c.JSON(http.StatusBadRequest, NewErrorResponse("权限ID格式错误", err.Error()))
		return
	}

	permission, exists := permissions[permissionID]
	if !exists {
		c.JSON(http.StatusNotFound, NewErrorResponse("权限不存在", fmt.Sprintf("ID: %d", permissionID)))
		return
	}

	response := model.PermissionResponse{
		ID:          permission.ID,
		Code:        permission.Code,
		Name:        permission.Name,
		Description: permission.Description,
		Resource:    permission.Resource,
		Action:      permission.Action,
		Category:    permission.Category,
		Status:      permission.Status,
		CreatedAt:   permission.CreatedAt,
		UpdatedAt:   permission.UpdatedAt,
	}

	c.JSON(http.StatusOK, NewSuccessResponse("获取权限成功", response))
}

// ===== 角色管理API =====

// createRole 创建角色
func createRole(c *gin.Context) {
	var req model.CreateRoleCommand
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, NewErrorResponse("请求参数错误", err.Error()))
		return
	}

	// 检查角色代码是否已存在
	for _, role := range roles {
		if role.Code == req.Code {
			c.JSON(http.StatusBadRequest, NewErrorResponse("角色代码已存在", req.Code))
			return
		}
	}

	// 创建角色
	role := &model.Role{
		Code:        req.Code,
		Name:        req.Name,
		Description: req.Description,
		Category:    req.Category,
		Status:      "active",
	}
	role.ID = code.MustNextID()
	role.CreatedAt = time.Now()
	role.UpdatedAt = time.Now()

	roles[role.ID] = role

	c.JSON(http.StatusCreated, NewSuccessResponse("创建角色成功", nil))
}

// listRoles 获取角色列表
func listRoles(c *gin.Context) {
	var roleList []model.RoleResponse
	for _, role := range roles {
		roleList = append(roleList, model.RoleResponse{
			ID:          role.ID,
			Code:        role.Code,
			Name:        role.Name,
			Description: role.Description,
			Category:    role.Category,
			Status:      role.Status,
			Permissions: []model.PermissionResponse{}, // 简化处理
			CreatedAt:   role.CreatedAt,
			UpdatedAt:   role.UpdatedAt,
		})
	}

	result := map[string]interface{}{
		"roles":       roleList,
		"total":       len(roleList),
		"page":        1,
		"size":        len(roleList),
		"total_pages": 1,
	}

	c.JSON(http.StatusOK, NewSuccessResponse("获取角色列表成功", result))
}

// getRole 获取角色
func getRole(c *gin.Context) {
	idStr := c.Param("id")
	var roleID int64
	if _, err := fmt.Sscanf(idStr, "%d", &roleID); err != nil {
		c.JSON(http.StatusBadRequest, NewErrorResponse("角色ID格式错误", err.Error()))
		return
	}

	role, exists := roles[roleID]
	if !exists {
		c.JSON(http.StatusNotFound, NewErrorResponse("角色不存在", fmt.Sprintf("ID: %d", roleID)))
		return
	}

	response := model.RoleResponse{
		ID:          role.ID,
		Code:        role.Code,
		Name:        role.Name,
		Description: role.Description,
		Category:    role.Category,
		Status:      role.Status,
		Permissions: []model.PermissionResponse{}, // 简化处理
		CreatedAt:   role.CreatedAt,
		UpdatedAt:   role.UpdatedAt,
	}

	c.JSON(http.StatusOK, NewSuccessResponse("获取角色成功", response))
}

// ===== 任务管理API（Event Sourcing模型）=====

// createTask 创建任务
func createTask(c *gin.Context) {
	var req model.TaskCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, NewErrorResponse("请求参数错误", err.Error()))
		return
	}

	// 创建任务（简化Event Sourcing实现）
	task := &model.TaskResponse{
		ID:          code.MustNextID(),
		Title:       req.Title,
		Description: req.Description,
		Status:      model.TaskStatusPending,
		Priority:    req.Priority,
		AssigneeID:  req.AssigneeID,
		CreatorID:   1, // 简化处理，假设当前用户ID为1
		ProjectID:   req.ProjectID,
		Progress:    0,
		Tags:        req.Tags,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		Version:     1,
	}

	if task.Priority == "" {
		task.Priority = model.TaskPriorityMedium
	}

	tasks[task.ID] = task

	c.JSON(http.StatusCreated, NewSuccessResponse("创建任务成功", task))
}

// listTasks 获取任务列表
func listTasks(c *gin.Context) {
	var taskList []*model.TaskResponse
	for _, task := range tasks {
		taskList = append(taskList, task)
	}

	result := model.TaskListResponse{
		Tasks:      make([]model.TaskResponse, len(taskList)),
		Total:      int64(len(taskList)),
		Page:       1,
		Size:       len(taskList),
		TotalPages: 1,
	}

	// 转换为值类型
	for i, task := range taskList {
		result.Tasks[i] = *task
	}

	c.JSON(http.StatusOK, NewSuccessResponse("获取任务列表成功", result))
}

// getTask 获取任务
func getTask(c *gin.Context) {
	idStr := c.Param("id")
	var taskID int64
	if _, err := fmt.Sscanf(idStr, "%d", &taskID); err != nil {
		c.JSON(http.StatusBadRequest, NewErrorResponse("任务ID格式错误", err.Error()))
		return
	}

	task, exists := tasks[taskID]
	if !exists {
		c.JSON(http.StatusNotFound, NewErrorResponse("任务不存在", fmt.Sprintf("ID: %d", taskID)))
		return
	}

	c.JSON(http.StatusOK, NewSuccessResponse("获取任务成功", task))
}

// startTask 开始任务
func startTask(c *gin.Context) {
	idStr := c.Param("id")
	var taskID int64
	if _, err := fmt.Sscanf(idStr, "%d", &taskID); err != nil {
		c.JSON(http.StatusBadRequest, NewErrorResponse("任务ID格式错误", err.Error()))
		return
	}

	task, exists := tasks[taskID]
	if !exists {
		c.JSON(http.StatusNotFound, NewErrorResponse("任务不存在", fmt.Sprintf("ID: %d", taskID)))
		return
	}

	if task.Status != model.TaskStatusPending {
		c.JSON(http.StatusBadRequest, NewErrorResponse("只有待处理的任务才能开始", string(task.Status)))
		return
	}

	// 更新任务状态（Event Sourcing事件：TaskStarted）
	task.Status = model.TaskStatusInProgress
	now := time.Now()
	task.StartedAt = &now
	task.UpdatedAt = now
	task.Version++

	c.JSON(http.StatusOK, NewSuccessResponse("开始任务成功", task))
}

// updateTaskProgress 更新任务进度
func updateTaskProgress(c *gin.Context) {
	idStr := c.Param("id")
	var taskID int64
	if _, err := fmt.Sscanf(idStr, "%d", &taskID); err != nil {
		c.JSON(http.StatusBadRequest, NewErrorResponse("任务ID格式错误", err.Error()))
		return
	}

	var req model.TaskProgressRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, NewErrorResponse("请求参数错误", err.Error()))
		return
	}

	task, exists := tasks[taskID]
	if !exists {
		c.JSON(http.StatusNotFound, NewErrorResponse("任务不存在", fmt.Sprintf("ID: %d", taskID)))
		return
	}

	if task.Status == model.TaskStatusCompleted || task.Status == model.TaskStatusCancelled {
		c.JSON(http.StatusBadRequest, NewErrorResponse("已完成或已取消的任务不能更新进度", string(task.Status)))
		return
	}

	// 更新进度（Event Sourcing事件：TaskProgressUpdated）
	task.Progress = req.Progress
	task.UpdatedAt = time.Now()
	task.Version++

	// 如果进度达到100%，自动完成任务
	if req.Progress == 100 && task.Status != model.TaskStatusCompleted {
		task.Status = model.TaskStatusCompleted
		now := time.Now()
		task.CompletedAt = &now
	}

	c.JSON(http.StatusOK, NewSuccessResponse("更新任务进度成功", task))
}

// completeTask 完成任务
func completeTask(c *gin.Context) {
	idStr := c.Param("id")
	var taskID int64
	if _, err := fmt.Sscanf(idStr, "%d", &taskID); err != nil {
		c.JSON(http.StatusBadRequest, NewErrorResponse("任务ID格式错误", err.Error()))
		return
	}

	task, exists := tasks[taskID]
	if !exists {
		c.JSON(http.StatusNotFound, NewErrorResponse("任务不存在", fmt.Sprintf("ID: %d", taskID)))
		return
	}

	if task.Status == model.TaskStatusCompleted {
		c.JSON(http.StatusBadRequest, NewErrorResponse("任务已经完成", ""))
		return
	}

	if task.Status == model.TaskStatusCancelled {
		c.JSON(http.StatusBadRequest, NewErrorResponse("已取消的任务不能完成", ""))
		return
	}

	// 完成任务（Event Sourcing事件：TaskCompleted）
	task.Status = model.TaskStatusCompleted
	task.Progress = 100
	now := time.Now()
	task.CompletedAt = &now
	task.UpdatedAt = now
	task.Version++

	c.JSON(http.StatusOK, NewSuccessResponse("完成任务成功", task))
}
