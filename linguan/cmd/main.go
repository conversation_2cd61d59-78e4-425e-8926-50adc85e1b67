package main

import (
	"context"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"

	"gochen/data/code"
	"linguan/internal/api"
	"linguan/internal/model"
	"linguan/internal/service"
)

// @title Linguan API
// @version 1.0
// @description 基于GoChen框架的三种业务模型演示项目
// @termsOfService http://swagger.io/terms/

// @contact.name API Support
// @contact.url http://www.swagger.io/support
// @contact.email <EMAIL>

// @license.name Apache 2.0
// @license.url http://www.apache.org/licenses/LICENSE-2.0.html

// @host localhost:8080
// @BasePath /api
func main() {
	// 初始化ID生成器
	code.InitGenerator(1, 1)

	// 初始化数据库
	db, err := initDatabase()
	if err != nil {
		log.Fatalf("初始化数据库失败: %v", err)
	}

	// 创建服务
	userService := service.NewUserService(db)
	permissionService := service.NewPermissionService(db)
	taskService := service.NewTaskService(db)

	// 初始化默认数据
	if err := initializeDefaultData(permissionService); err != nil {
		log.Printf("初始化默认数据失败: %v", err)
	}

	// 创建控制器
	userController := api.NewUserController(userService)
	permissionController := api.NewPermissionController(permissionService)
	taskController := api.NewTaskController(taskService)

	// 创建Gin引擎
	r := gin.Default()

	// 添加CORS中间件
	r.Use(corsMiddleware())

	// 注册Swagger
	r.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	// 注册API路由
	apiGroup := r.Group("/api")
	{
		userController.RegisterRoutes(apiGroup)
		permissionController.RegisterRoutes(apiGroup)
		taskController.RegisterRoutes(apiGroup)
	}

	// 添加健康检查端点
	r.GET("/health", healthCheck)

	// 添加系统信息端点
	r.GET("/info", systemInfo)

	// 启动HTTP服务器
	srv := &http.Server{
		Addr:    ":8080",
		Handler: r,
	}

	// 在goroutine中启动服务器
	go func() {
		log.Println("服务器启动在 :8080")
		log.Println("Swagger文档地址: http://localhost:8080/swagger/index.html")
		log.Println("健康检查地址: http://localhost:8080/health")
		log.Println("系统信息地址: http://localhost:8080/info")

		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("启动服务器失败: %v", err)
		}
	}()

	// 等待中断信号以优雅地关闭服务器
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Println("正在关闭服务器...")

	// 关闭任务服务
	if err := taskService.Stop(); err != nil {
		log.Printf("关闭任务服务失败: %v", err)
	}

	// 设置5秒的超时时间来关闭服务器
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := srv.Shutdown(ctx); err != nil {
		log.Fatal("服务器强制关闭:", err)
	}

	log.Println("服务器已退出")
}

// initDatabase 初始化数据库
func initDatabase() (*gorm.DB, error) {
	// 使用内存数据库避免CGO依赖问题
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		return nil, err
	}

	// 自动迁移数据库表
	err = db.AutoMigrate(
		&model.User{},
		&model.Permission{},
		&model.Role{},
		&model.UserRole{},
		&model.TaskComment{},
		&model.TaskHistory{},
	)
	if err != nil {
		return nil, err
	}

	log.Println("数据库初始化完成")
	return db, nil
}

// initializeDefaultData 初始化默认数据
func initializeDefaultData(permissionService *service.PermissionService) error {
	ctx := context.Background()

	// 初始化默认权限
	if err := permissionService.InitializeDefaultPermissions(ctx); err != nil {
		return err
	}

	// 初始化默认角色
	if err := permissionService.InitializeDefaultRoles(ctx); err != nil {
		return err
	}

	log.Println("默认数据初始化完成")
	return nil
}

// corsMiddleware CORS中间件
func corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	}
}

// healthCheck 健康检查
func healthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":    "healthy",
		"timestamp": time.Now(),
		"version":   "1.0.0",
		"models": gin.H{
			"user_model":       "simple",
			"permission_model": "cqrs",
			"task_model":       "event_sourcing",
		},
	})
}

// systemInfo 系统信息
func systemInfo(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"name":        "Linguan",
		"description": "基于GoChen框架的三种业务模型演示项目",
		"version":     "1.0.0",
		"framework":   "GoChen",
		"models": gin.H{
			"user": gin.H{
				"type":        "极简模型",
				"description": "用户管理采用极简模型，支持快速CRUD操作",
				"features": []string{
					"用户注册登录",
					"用户信息管理",
					"用户状态控制",
					"批量操作",
					"分页查询",
				},
			},
			"permission": gin.H{
				"type":        "CQRS模型",
				"description": "权限管理采用CQRS模型，命令查询分离",
				"features": []string{
					"权限管理",
					"角色管理",
					"用户角色分配",
					"权限检查",
					"命令查询分离",
				},
			},
			"task": gin.H{
				"type":        "Event Sourcing模型",
				"description": "任务管理采用Event Sourcing模型，完整事件溯源",
				"features": []string{
					"任务生命周期管理",
					"事件溯源",
					"任务历史追踪",
					"实时投影",
					"事件重放",
				},
			},
		},
		"endpoints": gin.H{
			"swagger": "/swagger/index.html",
			"health":  "/health",
			"info":    "/info",
			"api": gin.H{
				"users":       "/api/users",
				"permissions": "/api/permissions",
				"roles":       "/api/roles",
				"user_roles":  "/api/user-roles",
				"tasks":       "/api/tasks",
			},
		},
		"examples": gin.H{
			"create_user": gin.H{
				"method": "POST",
				"url":    "/api/users",
				"body": gin.H{
					"username": "testuser",
					"email":    "<EMAIL>",
					"password": "password123",
					"nickname": "测试用户",
				},
			},
			"create_task": gin.H{
				"method": "POST",
				"url":    "/api/tasks",
				"body": gin.H{
					"title":       "示例任务",
					"description": "这是一个示例任务",
					"priority":    "medium",
					"assignee_id": 1,
					"project_id":  1,
				},
			},
			"create_permission": gin.H{
				"method": "POST",
				"url":    "/api/permissions",
				"body": gin.H{
					"code":        "example.read",
					"name":        "示例读取权限",
					"description": "读取示例资源的权限",
					"resource":    "example",
					"action":      "read",
					"category":    "example",
				},
			},
		},
	})
}
