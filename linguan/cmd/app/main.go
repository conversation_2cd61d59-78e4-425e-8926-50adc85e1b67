// @title           Gochen API
// @version         1.0
// @description     Gochen 项目 API 文档
// @termsOfService  http://swagger.io/terms/

// @contact.name   API 支持
// @contact.url    http://www.swagger.io/support
// @contact.email  <EMAIL>

// @license.name  Apache 2.0
// @license.url   http://www.apache.org/licenses/LICENSE-2.0.html

// @host      localhost:8080
// @BasePath  /api

// @securityDefinitions.basic  BasicAuth

package main

import (
	"linguan/internal"
	"linguan/internal/config"
	"linguan/internal/controller"
	"linguan/internal/database"
	"linguan/internal/model"
	"linguan/internal/router"
	"linguan/internal/service"
)

// manualWiring 手动注入依赖
func manualWiring() *internal.App {
	// 创建配置
	cfg := config.NewConfig()

	// 创建数据库连接
	db := database.NewDB(cfg)

	// 自动迁移
	db.AutoMigrate(&model.User{})

	// 创建服务
	userService := service.NewUserService(db)

	// 创建控制器
	userController := controller.NewUserController(userService)

	// 创建路由
	r := router.NewRouter(userService, userController)

	// 创建应用程序
	app := internal.NewApp(cfg, db, r)

	return app
}

// @title Gochen API
// @version 1.0
// @description Gochen 项目的 RESTful API 服务
// @host localhost:8080
// @BasePath /
func main() {
	// 使用手动注入方式构建应用程序
	app := manualWiring()

	// 启动应用程序
	app.Start()
}
