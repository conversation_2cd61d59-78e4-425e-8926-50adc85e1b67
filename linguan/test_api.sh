#!/home/<USER>/.nix-profile/bin/bash

# Linguan API 测试脚本 - 验证三种业务模型
# 用户管理（极简模型）、权限管理（CQRS模型）、任务管理（Event Sourcing模型）

BASE_URL="http://localhost:8081"
echo "🚀 开始测试 Linguan API - 三种业务模型演示"
echo "📍 服务地址: $BASE_URL"
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试函数
test_api() {
    local method=$1
    local endpoint=$2
    local data=$3
    local description=$4
    
    echo -e "${BLUE}测试: $description${NC}"
    echo "请求: $method $endpoint"
    
    if [ -n "$data" ]; then
        response=$(curl -s -X $method "$BASE_URL$endpoint" \
            -H "Content-Type: application/json" \
            -d "$data")
    else
        response=$(curl -s -X $method "$BASE_URL$endpoint")
    fi
    
    # 检查响应
    if echo "$response" | jq -e '.code == 200 or .code == 201 or .status == "healthy"' > /dev/null 2>&1; then
        echo -e "${GREEN}✅ 成功${NC}"
        echo "$response" | jq .
    else
        echo -e "${RED}❌ 失败${NC}"
        echo "$response" | jq .
    fi
    echo ""
}

# 1. 系统健康检查
echo -e "${YELLOW}=== 1. 系统健康检查 ===${NC}"
test_api "GET" "/health" "" "系统健康状态"
test_api "GET" "/info" "" "系统信息"

# 2. 用户管理测试（极简模型）
echo -e "${YELLOW}=== 2. 用户管理测试（极简模型）===${NC}"

# 获取现有用户列表
test_api "GET" "/api/users" "" "获取用户列表"

# 创建新用户
test_api "POST" "/api/users" '{
    "username": "developer",
    "email": "<EMAIL>", 
    "password": "dev123456",
    "nickname": "开发者"
}' "创建新用户"

# 用户登录
test_api "POST" "/api/users/login" '{
    "username": "admin",
    "password": "admin123"
}' "管理员登录"

test_api "POST" "/api/users/login" '{
    "username": "developer", 
    "password": "dev123456"
}' "开发者登录"

# 3. 权限管理测试（CQRS模型）
echo -e "${YELLOW}=== 3. 权限管理测试（CQRS模型）===${NC}"

# 获取权限列表
test_api "GET" "/api/permissions" "" "获取权限列表"

# 创建新权限
test_api "POST" "/api/permissions" '{
    "code": "task.delete",
    "name": "删除任务",
    "description": "删除任务的权限",
    "resource": "task",
    "action": "delete", 
    "category": "task_management"
}' "创建删除任务权限"

test_api "POST" "/api/permissions" '{
    "code": "user.manage",
    "name": "用户管理",
    "description": "管理用户的权限",
    "resource": "user",
    "action": "manage",
    "category": "user_management"
}' "创建用户管理权限"

# 获取角色列表
test_api "GET" "/api/roles" "" "获取角色列表"

# 创建新角色
test_api "POST" "/api/roles" '{
    "code": "developer",
    "name": "开发者",
    "description": "开发者角色",
    "category": "development"
}' "创建开发者角色"

# 4. 任务管理测试（Event Sourcing模型）
echo -e "${YELLOW}=== 4. 任务管理测试（Event Sourcing模型）===${NC}"

# 获取任务列表
test_api "GET" "/api/tasks" "" "获取任务列表"

# 创建新任务
task_response=$(curl -s -X POST "$BASE_URL/api/tasks" \
    -H "Content-Type: application/json" \
    -d '{
        "title": "优化数据库性能",
        "description": "分析并优化数据库查询性能，提升系统响应速度",
        "priority": "medium",
        "assignee_id": 1,
        "project_id": 1,
        "tags": ["数据库", "性能", "优化"]
    }')

echo -e "${BLUE}测试: 创建新任务${NC}"
echo "请求: POST /api/tasks"
if echo "$task_response" | jq -e '.code == 200 or .code == 201' > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 成功${NC}"
    task_id=$(echo "$task_response" | jq -r '.data.id')
    echo "任务ID: $task_id"
    echo "$task_response" | jq .
else
    echo -e "${RED}❌ 失败${NC}"
    echo "$task_response" | jq .
fi
echo ""

# 如果任务创建成功，继续测试任务生命周期
if [ -n "$task_id" ] && [ "$task_id" != "null" ]; then
    # 开始任务
    test_api "POST" "/api/tasks/$task_id/start" "" "开始任务"
    
    # 更新任务进度
    test_api "PUT" "/api/tasks/$task_id/progress" '{
        "progress": 30
    }' "更新任务进度到30%"
    
    test_api "PUT" "/api/tasks/$task_id/progress" '{
        "progress": 80
    }' "更新任务进度到80%"
    
    # 获取单个任务详情
    test_api "GET" "/api/tasks/$task_id" "" "获取任务详情"
    
    # 完成任务
    test_api "POST" "/api/tasks/$task_id/complete" "" "完成任务"
fi

# 5. 综合测试
echo -e "${YELLOW}=== 5. 综合测试 ===${NC}"

# 再次检查系统状态
test_api "GET" "/health" "" "最终健康检查"

# 获取所有数据的最新状态
test_api "GET" "/api/users" "" "最终用户列表"
test_api "GET" "/api/permissions" "" "最终权限列表" 
test_api "GET" "/api/roles" "" "最终角色列表"
test_api "GET" "/api/tasks" "" "最终任务列表"

echo -e "${GREEN}🎉 测试完成！${NC}"
echo ""
echo -e "${BLUE}测试总结:${NC}"
echo "✅ 用户管理（极简模型）- 支持CRUD操作、登录验证"
echo "✅ 权限管理（CQRS模型）- 支持权限和角色管理"  
echo "✅ 任务管理（Event Sourcing模型）- 支持任务生命周期管理"
echo ""
echo -e "${YELLOW}访问地址:${NC}"
echo "🌐 系统信息: $BASE_URL/info"
echo "💚 健康检查: $BASE_URL/health"
echo "📖 API文档: 查看源码中的注释"
