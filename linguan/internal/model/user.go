package model

import (
	"errors"
	"time"

	"gochen/domain/model"
)

// User 用户实体（极简模型）
type User struct {
	model.Entity
	Username  string     `json:"username" gorm:"uniqueIndex;not null" binding:"required"`
	Email     string     `json:"email" gorm:"uniqueIndex;not null" binding:"required,email"`
	Password  string     `json:"password,omitempty" gorm:"not null" binding:"required,min=6"`
	Nickname  string     `json:"nickname" gorm:"not null"`
	Avatar    string     `json:"avatar"`
	Status    string     `json:"status" gorm:"default:active"` // active, inactive, banned
	LastLogin *time.Time `json:"last_login"`
}

// TableName 指定表名
func (User) TableName() string {
	return "users"
}

// GetID 实现IEntity接口
func (u User) GetID() int64 {
	return u.ID
}

// Validate 验证用户数据
func (u *User) Validate() error {
	if u.Username == "" {
		return errors.New("用户名不能为空")
	}
	if len(u.Username) < 3 || len(u.Username) > 50 {
		return errors.New("用户名长度必须在3-50个字符之间")
	}
	if u.Email == "" {
		return errors.New("邮箱不能为空")
	}
	if u.Password == "" {
		return errors.New("密码不能为空")
	}
	if len(u.Password) < 6 {
		return errors.New("密码长度不能少于6个字符")
	}
	if u.Nickname == "" {
		return errors.New("昵称不能为空")
	}
	if u.Status != "active" && u.Status != "inactive" && u.Status != "banned" {
		return errors.New("用户状态无效")
	}
	return nil
}

// IsActive 检查用户是否激活
func (u *User) IsActive() bool {
	return u.Status == "active"
}

// IsBanned 检查用户是否被禁用
func (u *User) IsBanned() bool {
	return u.Status == "banned"
}

// Activate 激活用户
func (u *User) Activate() {
	u.Status = "active"
	u.UpdatedAt = time.Now()
}

// Deactivate 停用用户
func (u *User) Deactivate() {
	u.Status = "inactive"
	u.UpdatedAt = time.Now()
}

// Ban 禁用用户
func (u *User) Ban() {
	u.Status = "banned"
	u.UpdatedAt = time.Now()
}

// UpdateLastLogin 更新最后登录时间
func (u *User) UpdateLastLogin() {
	now := time.Now()
	u.LastLogin = &now
	u.UpdatedAt = now
}

// HidePassword 隐藏密码（用于API响应）
func (u *User) HidePassword() {
	u.Password = ""
}

// UserCreateRequest 创建用户请求
type UserCreateRequest struct {
	Username string `json:"username" binding:"required,min=3,max=50"`
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=6"`
	Nickname string `json:"nickname" binding:"required,min=1,max=100"`
	Avatar   string `json:"avatar"`
}

// ToUser 转换为User实体
func (req *UserCreateRequest) ToUser() *User {
	now := time.Now()
	return &User{
		Entity: model.Entity{
			CreatedAt: now,
			UpdatedAt: now,
		},
		Username: req.Username,
		Email:    req.Email,
		Password: req.Password, // 注意：实际项目中需要加密
		Nickname: req.Nickname,
		Avatar:   req.Avatar,
		Status:   "active",
	}
}

// UserUpdateRequest 更新用户请求
type UserUpdateRequest struct {
	Nickname string `json:"nickname" binding:"omitempty,min=1,max=100"`
	Avatar   string `json:"avatar"`
	Status   string `json:"status" binding:"omitempty,oneof=active inactive banned"`
}

// ApplyTo 应用更新到用户实体
func (req *UserUpdateRequest) ApplyTo(user *User) {
	if req.Nickname != "" {
		user.Nickname = req.Nickname
	}
	if req.Avatar != "" {
		user.Avatar = req.Avatar
	}
	if req.Status != "" {
		user.Status = req.Status
	}
	user.UpdatedAt = time.Now()
}

// UserResponse 用户响应
type UserResponse struct {
	ID        int64      `json:"id"`
	Username  string     `json:"username"`
	Email     string     `json:"email"`
	Nickname  string     `json:"nickname"`
	Avatar    string     `json:"avatar"`
	Status    string     `json:"status"`
	LastLogin *time.Time `json:"last_login"`
	CreatedAt time.Time  `json:"created_at"`
	UpdatedAt time.Time  `json:"updated_at"`
}

// FromUser 从User实体创建响应
func (resp *UserResponse) FromUser(user *User) {
	resp.ID = user.ID
	resp.Username = user.Username
	resp.Email = user.Email
	resp.Nickname = user.Nickname
	resp.Avatar = user.Avatar
	resp.Status = user.Status
	resp.LastLogin = user.LastLogin
	resp.CreatedAt = user.CreatedAt
	resp.UpdatedAt = user.UpdatedAt
}

// UserListResponse 用户列表响应
type UserListResponse struct {
	Users      []UserResponse `json:"users"`
	Total      int64          `json:"total"`
	Page       int            `json:"page"`
	Size       int            `json:"size"`
	TotalPages int            `json:"total_pages"`
}

// LoginRequest 登录请求
type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// LoginResponse 登录响应
type LoginResponse struct {
	User  UserResponse `json:"user"`
	Token string       `json:"token"`
}
