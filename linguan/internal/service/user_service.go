package service

import (
	"context"
	"crypto/md5"
	"fmt"
	"time"

	"gochen/data/code"
	"gochen/domain/repo"
	"gochen/domain/service"
	"linguan/internal/model"
	"linguan/internal/repository"

	"gorm.io/gorm"
)

// UserService 用户服务（极简模型）
type UserService struct {
	service.IService[model.User]
	userRepo *repository.UserRepository
	db       *gorm.DB
}

// NewUserService 创建用户服务
func NewUserService(db *gorm.DB) *UserService {
	userRepo := repository.NewUserRepository(db).(*repository.UserRepository)
	baseService := service.NewService("user", userRepo)

	return &UserService{
		IService: baseService,
		userRepo: userRepo,
		db:       db,
	}
}

// CreateUser 创建用户
func (s *UserService) CreateUser(ctx context.Context, req *model.UserCreateRequest) (*model.UserResponse, error) {
	// 检查用户名是否已存在
	if _, err := s.userRepo.FindByUsername(ctx, req.Username); err == nil {
		return nil, fmt.Errorf("用户名已存在: %s", req.Username)
	}

	// 检查邮箱是否已存在
	if _, err := s.userRepo.FindByEmail(ctx, req.Email); err == nil {
		return nil, fmt.Errorf("邮箱已存在: %s", req.Email)
	}

	// 创建用户实体
	user := req.ToUser()
	user.ID = code.MustNextID()

	// 加密密码（简化处理，实际项目应使用bcrypt）
	user.Password = s.hashPassword(req.Password)

	// 验证用户数据
	if err := user.Validate(); err != nil {
		return nil, fmt.Errorf("用户数据验证失败: %w", err)
	}

	// 保存用户
	if err := s.userRepo.Save(ctx, user); err != nil {
		return nil, fmt.Errorf("创建用户失败: %w", err)
	}

	// 隐藏密码并返回响应
	user.HidePassword()
	var response model.UserResponse
	response.FromUser(user)

	return &response, nil
}

// Login 用户登录
func (s *UserService) Login(ctx context.Context, req *model.LoginRequest) (*model.LoginResponse, error) {
	// 根据用户名查找用户
	user, err := s.userRepo.FindByUsername(ctx, req.Username)
	if err != nil {
		return nil, fmt.Errorf("用户名或密码错误")
	}

	// 验证密码
	if !s.verifyPassword(req.Password, user.Password) {
		return nil, fmt.Errorf("用户名或密码错误")
	}

	// 检查用户状态
	if !user.IsActive() {
		return nil, fmt.Errorf("用户账号已被禁用")
	}

	// 更新最后登录时间
	user.UpdateLastLogin()
	if err := s.userRepo.Update(ctx, user); err != nil {
		// 记录错误但不影响登录
		fmt.Printf("更新最后登录时间失败: %v\n", err)
	}

	// 生成token（简化处理，实际项目应使用JWT）
	token := s.generateToken(user.ID)

	// 隐藏密码
	user.HidePassword()
	var userResponse model.UserResponse
	userResponse.FromUser(user)

	return &model.LoginResponse{
		User:  userResponse,
		Token: token,
	}, nil
}

// ListUsers 获取用户列表
func (s *UserService) ListUsers(ctx context.Context, page, size int, filters map[string]string) (*model.UserListResponse, error) {
	if page <= 0 {
		page = 1
	}
	if size <= 0 {
		size = 10
	}
	if size > 100 {
		size = 100
	}

	options := &repo.QueryOptions{
		Page:    page,
		Size:    size,
		Sort:    "created_at",
		Order:   "desc",
		Filters: filters,
	}

	result, err := s.userRepo.FindWithOptions(ctx, options)
	if err != nil {
		return nil, fmt.Errorf("获取用户列表失败: %w", err)
	}

	// 转换为响应格式
	var userResponses []model.UserResponse
	for _, user := range result.Data {
		user.HidePassword()
		var response model.UserResponse
		response.FromUser(user)
		userResponses = append(userResponses, response)
	}

	return &model.UserListResponse{
		Users:      userResponses,
		Total:      result.Total,
		Page:       result.Page,
		Size:       result.Size,
		TotalPages: result.TotalPages,
	}, nil
}

// GetUser 获取用户
func (s *UserService) GetUser(ctx context.Context, userID int64) (*model.UserResponse, error) {
	user, err := s.userRepo.Get(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("获取用户失败: %w", err)
	}

	user.HidePassword()
	var response model.UserResponse
	response.FromUser(user)

	return &response, nil
}

// UpdateUser 更新用户
func (s *UserService) UpdateUser(ctx context.Context, userID int64, req *model.UserUpdateRequest) (*model.UserResponse, error) {
	// 获取用户
	user, err := s.userRepo.Get(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("获取用户失败: %w", err)
	}

	// 应用更新
	req.ApplyTo(user)

	// 验证用户数据
	if err := user.Validate(); err != nil {
		return nil, fmt.Errorf("用户数据验证失败: %w", err)
	}

	// 保存更新
	if err := s.userRepo.Update(ctx, user); err != nil {
		return nil, fmt.Errorf("更新用户失败: %w", err)
	}

	// 隐藏密码并返回响应
	user.HidePassword()
	var response model.UserResponse
	response.FromUser(user)

	return &response, nil
}

// DeleteUser 删除用户
func (s *UserService) DeleteUser(ctx context.Context, userID int64) error {
	// 检查用户是否存在
	exists, err := s.userRepo.Exists(ctx, userID)
	if err != nil {
		return fmt.Errorf("检查用户存在性失败: %w", err)
	}
	if !exists {
		return fmt.Errorf("用户不存在: %d", userID)
	}

	// 删除用户
	if err := s.userRepo.Delete(ctx, userID); err != nil {
		return fmt.Errorf("删除用户失败: %w", err)
	}

	return nil
}

// ActivateUser 激活用户
func (s *UserService) ActivateUser(ctx context.Context, userID int64) error {
	user, err := s.userRepo.Get(ctx, userID)
	if err != nil {
		return fmt.Errorf("获取用户失败: %w", err)
	}

	user.Activate()

	if err := s.userRepo.Update(ctx, user); err != nil {
		return fmt.Errorf("激活用户失败: %w", err)
	}

	return nil
}

// DeactivateUser 停用用户
func (s *UserService) DeactivateUser(ctx context.Context, userID int64) error {
	user, err := s.userRepo.Get(ctx, userID)
	if err != nil {
		return fmt.Errorf("获取用户失败: %w", err)
	}

	user.Deactivate()

	if err := s.userRepo.Update(ctx, user); err != nil {
		return fmt.Errorf("停用用户失败: %w", err)
	}

	return nil
}

// BanUser 禁用用户
func (s *UserService) BanUser(ctx context.Context, userID int64) error {
	user, err := s.userRepo.Get(ctx, userID)
	if err != nil {
		return fmt.Errorf("获取用户失败: %w", err)
	}

	user.Ban()

	if err := s.userRepo.Update(ctx, user); err != nil {
		return fmt.Errorf("禁用用户失败: %w", err)
	}

	return nil
}

// hashPassword 加密密码（简化实现）
func (s *UserService) hashPassword(password string) string {
	hash := md5.Sum([]byte(password + "salt"))
	return fmt.Sprintf("%x", hash)
}

// verifyPassword 验证密码
func (s *UserService) verifyPassword(password, hashedPassword string) bool {
	return s.hashPassword(password) == hashedPassword
}

// generateToken 生成token（简化实现）
func (s *UserService) generateToken(userID int64) string {
	timestamp := time.Now().Unix()
	data := fmt.Sprintf("%d:%d", userID, timestamp)
	hash := md5.Sum([]byte(data + "secret"))
	return fmt.Sprintf("%x", hash)
}
