package service

import (
	"context"
	"fmt"

	"gochen/app/eventsourcing"
	"gochen/data/code"
	"gochen/domain/model/eventaggregate"
	"gochen/event"
	"linguan/internal/model"
	"linguan/internal/repository"

	"gorm.io/gorm"
)

// TaskService 任务服务（Event Sourcing模型）
type TaskService struct {
	esService      *eventsourcing.EventSourcingAppService
	taskProjection *repository.TaskProjection
	db             *gorm.DB
}

// NewTaskService 创建任务服务
func NewTaskService(db *gorm.DB) *TaskService {
	// 创建Event Sourcing组件
	eventStore := event.NewMemoryEventStore()
	snapshotStore := event.NewMemorySnapshotStore()
	eventBus := event.NewMemoryEventBus(2, 1000)

	// 创建Event Sourcing应用服务
	esService := eventsourcing.NewEventSourcingAppService(eventStore, eventBus, snapshotStore)

	// 创建任务投影
	taskProjection := repository.NewTaskProjection()

	service := &TaskService{
		esService:      esService,
		taskProjection: taskProjection,
		db:             db,
	}

	// 初始化服务
	service.initialize()

	return service
}

// initialize 初始化服务
func (s *TaskService) initialize() {
	ctx := context.Background()

	// 启动Event Sourcing服务
	if err := s.esService.Start(ctx); err != nil {
		panic(fmt.Sprintf("启动Event Sourcing服务失败: %v", err))
	}

	// 注册任务聚合
	s.esService.RegisterAggregate("Task", func(id int64) eventaggregate.IEventSourcedAggregate {
		return model.NewTask(id)
	})

	// 注册任务投影
	if err := s.esService.RegisterProjection(s.taskProjection); err != nil {
		panic(fmt.Sprintf("注册任务投影失败: %v", err))
	}

	// 启动投影
	if err := s.esService.StartProjection(ctx, "TaskProjection"); err != nil {
		panic(fmt.Sprintf("启动任务投影失败: %v", err))
	}
}

// CreateTask 创建任务
func (s *TaskService) CreateTask(ctx context.Context, req *model.TaskCreateRequest, creatorID int64) (*model.TaskResponse, error) {
	// 生成任务ID
	taskID := code.MustNextID()

	// 创建任务聚合
	task := model.NewTask(taskID)

	// 设置默认值
	priority := req.Priority
	if priority == "" {
		priority = model.TaskPriorityMedium
	}

	// 创建任务
	if err := task.CreateTask(
		req.Title,
		req.Description,
		creatorID,
		req.AssigneeID,
		req.ProjectID,
		priority,
		req.DueDate,
	); err != nil {
		return nil, fmt.Errorf("创建任务失败: %w", err)
	}

	// 添加标签
	for _, tag := range req.Tags {
		if err := task.AddTag(tag, creatorID); err != nil {
			return nil, fmt.Errorf("添加标签失败: %w", err)
		}
	}

	// 保存聚合
	if err := s.esService.SaveAggregate(ctx, task); err != nil {
		return nil, fmt.Errorf("保存任务聚合失败: %w", err)
	}

	// 等待投影更新
	// 在实际项目中，这里应该使用更好的同步机制
	// time.Sleep(100 * time.Millisecond)

	// 从投影获取任务
	taskResp, exists := s.taskProjection.GetTask(taskID)
	if !exists {
		return nil, fmt.Errorf("任务创建后未找到投影")
	}

	return taskResp, nil
}

// GetTask 获取任务
func (s *TaskService) GetTask(ctx context.Context, taskID int64) (*model.TaskResponse, error) {
	taskResp, exists := s.taskProjection.GetTask(taskID)
	if !exists {
		return nil, fmt.Errorf("任务不存在: %d", taskID)
	}

	return taskResp, nil
}

// UpdateTask 更新任务
func (s *TaskService) UpdateTask(ctx context.Context, taskID int64, req *model.TaskUpdateRequest, updatedBy int64) (*model.TaskResponse, error) {
	// 获取任务聚合
	aggregateInterface, err := s.esService.GetAggregate(ctx, "Task", taskID)
	if err != nil {
		return nil, fmt.Errorf("获取任务聚合失败: %w", err)
	}

	task, ok := aggregateInterface.(*model.Task)
	if !ok {
		return nil, fmt.Errorf("聚合类型转换失败")
	}

	// 设置默认值
	priority := req.Priority
	if priority == "" {
		priority = task.Priority
	}

	// 更新任务
	if err := task.UpdateTask(req.Title, req.Description, priority, req.DueDate, updatedBy); err != nil {
		return nil, fmt.Errorf("更新任务失败: %w", err)
	}

	// 保存聚合
	if err := s.esService.SaveAggregate(ctx, task); err != nil {
		return nil, fmt.Errorf("保存任务聚合失败: %w", err)
	}

	// 从投影获取更新后的任务
	taskResp, exists := s.taskProjection.GetTask(taskID)
	if !exists {
		return nil, fmt.Errorf("任务更新后未找到投影")
	}

	return taskResp, nil
}

// AssignTask 分配任务
func (s *TaskService) AssignTask(ctx context.Context, taskID int64, req *model.TaskAssignRequest, assignedBy int64) (*model.TaskResponse, error) {
	// 获取任务聚合
	aggregateInterface, err := s.esService.GetAggregate(ctx, "Task", taskID)
	if err != nil {
		return nil, fmt.Errorf("获取任务聚合失败: %w", err)
	}

	task, ok := aggregateInterface.(*model.Task)
	if !ok {
		return nil, fmt.Errorf("聚合类型转换失败")
	}

	// 分配任务
	if err := task.AssignTask(req.AssigneeID, assignedBy); err != nil {
		return nil, fmt.Errorf("分配任务失败: %w", err)
	}

	// 保存聚合
	if err := s.esService.SaveAggregate(ctx, task); err != nil {
		return nil, fmt.Errorf("保存任务聚合失败: %w", err)
	}

	// 从投影获取更新后的任务
	taskResp, exists := s.taskProjection.GetTask(taskID)
	if !exists {
		return nil, fmt.Errorf("任务分配后未找到投影")
	}

	return taskResp, nil
}

// StartTask 开始任务
func (s *TaskService) StartTask(ctx context.Context, taskID int64, startedBy int64) (*model.TaskResponse, error) {
	// 获取任务聚合
	aggregateInterface, err := s.esService.GetAggregate(ctx, "Task", taskID)
	if err != nil {
		return nil, fmt.Errorf("获取任务聚合失败: %w", err)
	}

	task, ok := aggregateInterface.(*model.Task)
	if !ok {
		return nil, fmt.Errorf("聚合类型转换失败")
	}

	// 开始任务
	if err := task.StartTask(startedBy); err != nil {
		return nil, fmt.Errorf("开始任务失败: %w", err)
	}

	// 保存聚合
	if err := s.esService.SaveAggregate(ctx, task); err != nil {
		return nil, fmt.Errorf("保存任务聚合失败: %w", err)
	}

	// 从投影获取更新后的任务
	taskResp, exists := s.taskProjection.GetTask(taskID)
	if !exists {
		return nil, fmt.Errorf("任务开始后未找到投影")
	}

	return taskResp, nil
}

// UpdateTaskProgress 更新任务进度
func (s *TaskService) UpdateTaskProgress(ctx context.Context, taskID int64, req *model.TaskProgressRequest, updatedBy int64) (*model.TaskResponse, error) {
	// 获取任务聚合
	aggregateInterface, err := s.esService.GetAggregate(ctx, "Task", taskID)
	if err != nil {
		return nil, fmt.Errorf("获取任务聚合失败: %w", err)
	}

	task, ok := aggregateInterface.(*model.Task)
	if !ok {
		return nil, fmt.Errorf("聚合类型转换失败")
	}

	// 更新进度
	if err := task.UpdateProgress(req.Progress, updatedBy); err != nil {
		return nil, fmt.Errorf("更新任务进度失败: %w", err)
	}

	// 保存聚合
	if err := s.esService.SaveAggregate(ctx, task); err != nil {
		return nil, fmt.Errorf("保存任务聚合失败: %w", err)
	}

	// 从投影获取更新后的任务
	taskResp, exists := s.taskProjection.GetTask(taskID)
	if !exists {
		return nil, fmt.Errorf("任务进度更新后未找到投影")
	}

	return taskResp, nil
}

// CompleteTask 完成任务
func (s *TaskService) CompleteTask(ctx context.Context, taskID int64, completedBy int64) (*model.TaskResponse, error) {
	// 获取任务聚合
	aggregateInterface, err := s.esService.GetAggregate(ctx, "Task", taskID)
	if err != nil {
		return nil, fmt.Errorf("获取任务聚合失败: %w", err)
	}

	task, ok := aggregateInterface.(*model.Task)
	if !ok {
		return nil, fmt.Errorf("聚合类型转换失败")
	}

	// 完成任务
	if err := task.CompleteTask(completedBy); err != nil {
		return nil, fmt.Errorf("完成任务失败: %w", err)
	}

	// 保存聚合
	if err := s.esService.SaveAggregate(ctx, task); err != nil {
		return nil, fmt.Errorf("保存任务聚合失败: %w", err)
	}

	// 从投影获取更新后的任务
	taskResp, exists := s.taskProjection.GetTask(taskID)
	if !exists {
		return nil, fmt.Errorf("任务完成后未找到投影")
	}

	return taskResp, nil
}

// CancelTask 取消任务
func (s *TaskService) CancelTask(ctx context.Context, taskID int64, cancelledBy int64, reason string) (*model.TaskResponse, error) {
	// 获取任务聚合
	aggregateInterface, err := s.esService.GetAggregate(ctx, "Task", taskID)
	if err != nil {
		return nil, fmt.Errorf("获取任务聚合失败: %w", err)
	}

	task, ok := aggregateInterface.(*model.Task)
	if !ok {
		return nil, fmt.Errorf("聚合类型转换失败")
	}

	// 取消任务
	if err := task.CancelTask(cancelledBy, reason); err != nil {
		return nil, fmt.Errorf("取消任务失败: %w", err)
	}

	// 保存聚合
	if err := s.esService.SaveAggregate(ctx, task); err != nil {
		return nil, fmt.Errorf("保存任务聚合失败: %w", err)
	}

	// 从投影获取更新后的任务
	taskResp, exists := s.taskProjection.GetTask(taskID)
	if !exists {
		return nil, fmt.Errorf("任务取消后未找到投影")
	}

	return taskResp, nil
}

// AddTaskComment 添加任务评论
func (s *TaskService) AddTaskComment(ctx context.Context, taskID int64, req *model.TaskCommentRequest, authorID int64) error {
	// 获取任务聚合
	aggregateInterface, err := s.esService.GetAggregate(ctx, "Task", taskID)
	if err != nil {
		return fmt.Errorf("获取任务聚合失败: %w", err)
	}

	task, ok := aggregateInterface.(*model.Task)
	if !ok {
		return fmt.Errorf("聚合类型转换失败")
	}

	// 添加评论
	if err := task.AddComment(req.Content, authorID); err != nil {
		return fmt.Errorf("添加任务评论失败: %w", err)
	}

	// 保存聚合
	if err := s.esService.SaveAggregate(ctx, task); err != nil {
		return fmt.Errorf("保存任务聚合失败: %w", err)
	}

	return nil
}

// ListTasks 获取任务列表
func (s *TaskService) ListTasks(ctx context.Context) (*model.TaskListResponse, error) {
	tasks := s.taskProjection.GetTasks()

	// 转换为值类型
	taskValues := make([]model.TaskResponse, len(tasks))
	for i, task := range tasks {
		taskValues[i] = *task
	}

	return &model.TaskListResponse{
		Tasks:      taskValues,
		Total:      int64(len(tasks)),
		Page:       1,
		Size:       len(tasks),
		TotalPages: 1,
	}, nil
}

// GetTasksByStatus 根据状态获取任务
func (s *TaskService) GetTasksByStatus(ctx context.Context, status model.TaskStatus) ([]*model.TaskResponse, error) {
	return s.taskProjection.GetTasksByStatus(status), nil
}

// GetTasksByAssignee 根据分配人获取任务
func (s *TaskService) GetTasksByAssignee(ctx context.Context, assigneeID int64) ([]*model.TaskResponse, error) {
	return s.taskProjection.GetTasksByAssignee(assigneeID), nil
}

// GetTaskHistory 获取任务历史
func (s *TaskService) GetTaskHistory(ctx context.Context, taskID int64) ([]event.Event, error) {
	return s.esService.GetEventHistory(ctx, taskID)
}

// Stop 停止服务
func (s *TaskService) Stop() error {
	return s.esService.Stop()
}
