package repository

import (
	"context"
	"fmt"

	"gochen/domain/repo"
	"linguan/internal/model"

	"gorm.io/gorm"
)

// UserRepository 用户仓库实现（极简模型）
type UserRepository struct {
	db *gorm.DB
}

// NewUserRepository 创建用户仓库
func NewUserRepository(db *gorm.DB) repo.IRepo[model.User] {
	return &UserRepository{db: db}
}

// Get 获取用户
func (r *UserRepository) Get(ctx context.Context, id int64) (*model.User, error) {
	var user model.User
	err := r.db.WithContext(ctx).First(&user, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("用户不存在: %d", id)
		}
		return nil, fmt.Errorf("获取用户失败: %w", err)
	}
	return &user, nil
}

// Save 保存用户
func (r *UserRepository) Save(ctx context.Context, user *model.User) error {
	err := r.db.WithContext(ctx).Create(user).Error
	if err != nil {
		return fmt.Errorf("保存用户失败: %w", err)
	}
	return nil
}

// Update 更新用户
func (r *UserRepository) Update(ctx context.Context, user *model.User) error {
	err := r.db.WithContext(ctx).Save(user).Error
	if err != nil {
		return fmt.Errorf("更新用户失败: %w", err)
	}
	return nil
}

// Delete 删除用户
func (r *UserRepository) Delete(ctx context.Context, id int64) error {
	err := r.db.WithContext(ctx).Delete(&model.User{}, id).Error
	if err != nil {
		return fmt.Errorf("删除用户失败: %w", err)
	}
	return nil
}

// SaveBatch 批量保存用户
func (r *UserRepository) SaveBatch(ctx context.Context, users []*model.User) error {
	err := r.db.WithContext(ctx).CreateInBatches(users, 100).Error
	if err != nil {
		return fmt.Errorf("批量保存用户失败: %w", err)
	}
	return nil
}

// UpdateBatch 批量更新用户
func (r *UserRepository) UpdateBatch(ctx context.Context, users []*model.User) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, user := range users {
			if err := tx.Save(user).Error; err != nil {
				return fmt.Errorf("批量更新用户失败: %w", err)
			}
		}
		return nil
	})
}

// DeleteBatch 批量删除用户
func (r *UserRepository) DeleteBatch(ctx context.Context, ids []int64) error {
	err := r.db.WithContext(ctx).Delete(&model.User{}, ids).Error
	if err != nil {
		return fmt.Errorf("批量删除用户失败: %w", err)
	}
	return nil
}

// Find 查找用户
func (r *UserRepository) Find(ctx context.Context, query *map[string]string) ([]*model.User, error) {
	var users []*model.User
	db := r.db.WithContext(ctx)

	if query != nil {
		for key, value := range *query {
			switch key {
			case "username":
				db = db.Where("username LIKE ?", "%"+value+"%")
			case "email":
				db = db.Where("email LIKE ?", "%"+value+"%")
			case "status":
				db = db.Where("status = ?", value)
			case "nickname":
				db = db.Where("nickname LIKE ?", "%"+value+"%")
			}
		}
	}

	err := db.Find(&users).Error
	if err != nil {
		return nil, fmt.Errorf("查找用户失败: %w", err)
	}

	return users, nil
}

// FindWithOptions 带选项查找用户
func (r *UserRepository) FindWithOptions(ctx context.Context, options *repo.QueryOptions) (*repo.PagedResult[model.User], error) {
	var users []*model.User
	var total int64

	db := r.db.WithContext(ctx).Model(&model.User{})

	// 应用过滤条件
	if options.Filters != nil {
		for key, value := range options.Filters {
			switch key {
			case "username":
				db = db.Where("username LIKE ?", "%"+value+"%")
			case "email":
				db = db.Where("email LIKE ?", "%"+value+"%")
			case "status":
				db = db.Where("status = ?", value)
			case "nickname":
				db = db.Where("nickname LIKE ?", "%"+value+"%")
			}
		}
	}

	// 获取总数
	if err := db.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("获取用户总数失败: %w", err)
	}

	// 应用排序
	if options.Sort != "" {
		orderClause := options.Sort
		if options.Order != "" {
			orderClause += " " + options.Order
		}
		db = db.Order(orderClause)
	} else {
		db = db.Order("created_at DESC")
	}

	// 应用分页
	offset := (options.Page - 1) * options.Size
	db = db.Offset(offset).Limit(options.Size)

	// 字段选择
	if len(options.Fields) > 0 {
		db = db.Select(options.Fields)
	}

	// 执行查询
	if err := db.Find(&users).Error; err != nil {
		return nil, fmt.Errorf("分页查询用户失败: %w", err)
	}

	// 计算总页数
	totalPages := int((total + int64(options.Size) - 1) / int64(options.Size))

	return &repo.PagedResult[model.User]{
		Data:       users,
		Total:      total,
		Page:       options.Page,
		Size:       options.Size,
		TotalPages: totalPages,
	}, nil
}

// Count 计算用户数量
func (r *UserRepository) Count(ctx context.Context, query *map[string]string) (int64, error) {
	var count int64
	db := r.db.WithContext(ctx).Model(&model.User{})

	if query != nil {
		for key, value := range *query {
			switch key {
			case "username":
				db = db.Where("username LIKE ?", "%"+value+"%")
			case "email":
				db = db.Where("email LIKE ?", "%"+value+"%")
			case "status":
				db = db.Where("status = ?", value)
			case "nickname":
				db = db.Where("nickname LIKE ?", "%"+value+"%")
			}
		}
	}

	err := db.Count(&count).Error
	if err != nil {
		return 0, fmt.Errorf("计算用户数量失败: %w", err)
	}

	return count, nil
}

// Exists 检查用户是否存在
func (r *UserRepository) Exists(ctx context.Context, id int64) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&model.User{}).Where("id = ?", id).Count(&count).Error
	if err != nil {
		return false, fmt.Errorf("检查用户存在性失败: %w", err)
	}
	return count > 0, nil
}

// FindByIds 根据ID列表查找用户
func (r *UserRepository) FindByIds(ctx context.Context, ids []int64) ([]*model.User, error) {
	var users []*model.User
	err := r.db.WithContext(ctx).Where("id IN ?", ids).Find(&users).Error
	if err != nil {
		return nil, fmt.Errorf("根据ID列表查找用户失败: %w", err)
	}

	return users, nil
}

// FindByUsername 根据用户名查找用户
func (r *UserRepository) FindByUsername(ctx context.Context, username string) (*model.User, error) {
	var user model.User
	err := r.db.WithContext(ctx).Where("username = ?", username).First(&user).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("用户不存在: %s", username)
		}
		return nil, fmt.Errorf("根据用户名查找用户失败: %w", err)
	}
	return &user, nil
}

// FindByEmail 根据邮箱查找用户
func (r *UserRepository) FindByEmail(ctx context.Context, email string) (*model.User, error) {
	var user model.User
	err := r.db.WithContext(ctx).Where("email = ?", email).First(&user).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("用户不存在: %s", email)
		}
		return nil, fmt.Errorf("根据邮箱查找用户失败: %w", err)
	}
	return &user, nil
}

// UpdateLastLogin 更新最后登录时间
func (r *UserRepository) UpdateLastLogin(ctx context.Context, userID int64) error {
	err := r.db.WithContext(ctx).Model(&model.User{}).
		Where("id = ?", userID).
		Update("last_login", gorm.Expr("NOW()")).Error
	if err != nil {
		return fmt.Errorf("更新最后登录时间失败: %w", err)
	}
	return nil
}

// GetActiveUsers 获取活跃用户
func (r *UserRepository) GetActiveUsers(ctx context.Context) ([]*model.User, error) {
	var users []*model.User
	err := r.db.WithContext(ctx).Where("status = ?", "active").Find(&users).Error
	if err != nil {
		return nil, fmt.Errorf("获取活跃用户失败: %w", err)
	}
	return users, nil
}

// GetUsersByStatus 根据状态获取用户
func (r *UserRepository) GetUsersByStatus(ctx context.Context, status string) ([]*model.User, error) {
	var users []*model.User
	err := r.db.WithContext(ctx).Where("status = ?", status).Find(&users).Error
	if err != nil {
		return nil, fmt.Errorf("根据状态获取用户失败: %w", err)
	}
	return users, nil
}
